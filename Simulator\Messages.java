package Simulator;

/**
 * Auteurs: Oussama GUELFAA, Houssam ALLALI & Noa AKAYAD
 * Date: 25-05-2025
 *
 * Tous les messages que les robots s'envoient pour communiquer.
 * C'est comme WhatsApp mais pour des robots ! Ils se disent qui fait quoi,
 * qui veut quelle tâche, qui a gagné les enchères, etc.
 */

/**
 * Une enchère = quand un robot dit "moi je veux cette tâche pour X points"
 */
class Enchere implements Comparable<Enchere> {
    private String robotId;
    private String taskId;
    private double bidAmount;
    private long timestamp;

    public Enchere(String robotId, String taskId, double bidAmount) {
        this.robotId = robotId;
        this.taskId = taskId;
        this.bidAmount = bidAmount;
        this.timestamp = System.currentTimeMillis();

        LogManager.getInstance().logCoordination(robotId,
            "Offre créée pour tâche " + taskId + " montant " + String.format("%.2f", bidAmount));
    }

    public String getRobotId() { return robotId; }
    public String getTaskId() { return taskId; }
    public double getBidAmount() { return bidAmount; }
    public long getTimestamp() { return timestamp; }

    @Override
    public int compareTo(Enchere other) {
        // Pour trier les enchères : plus haut montant = meilleur
        int result = Double.compare(other.bidAmount, this.bidAmount);
        if (result != 0) return result;
        // Si deux robots ont le même montant, celui qui a envoyé en premier gagne
        return Long.compare(this.timestamp, other.timestamp);
    }

    @Override
    public String toString() {
        return "Enchere{robot='" + robotId + "', tache='" + taskId +
               "', montant=" + String.format("%.2f", bidAmount) + '}';
    }
}

/**
 * La classe mère de tous les messages
 * Tous les messages ont un expéditeur, un timestamp et un type
 */
abstract class Message {
    private MyTransitRobot sender;
    private long timestamp;
    private String messageType;

    public Message(MyTransitRobot sender, String messageType) {
        this.sender = sender;
        this.messageType = messageType;
        this.timestamp = System.currentTimeMillis();

        LogManager.getInstance().logCoordination(sender.getName(),
            "Création message " + messageType);
    }

    public MyTransitRobot getSender() { return sender; }
    public long getTimestamp() { return timestamp; }
    public String getMessageType() { return messageType; }

    public abstract String serialize();
    public abstract void process(MyTransitRobot receiver);
}



/**
 * Message contenant une offre d'enchère
 */
class MessageOffre extends Message {
    private Enchere bid;
    private static final String TYPE = "OFFRE";

    public MessageOffre(MyTransitRobot sender, Enchere bid) {
        super(sender, TYPE);
        this.bid = bid;

        LogManager.getInstance().logCoordination(sender.getName(),
            "Envoi offre pour tâche " + bid.getTaskId());
    }

    public Enchere getBid() { return bid; }

    @Override
    public String serialize() {
        return TYPE + "|" + bid.getRobotId() + "|" + bid.getTaskId() + "|" +
               bid.getBidAmount() + "|" + bid.getTimestamp();
    }

    @Override
    public void process(MyTransitRobot receiver) {
        receiver.getCoordinateurTaches().handleBid(bid);
        LogManager.getInstance().logCoordination(receiver.getName(),
            "Offre reçue de " + bid.getRobotId() + " pour tâche " + bid.getTaskId());
    }
}



/**
 * Message d'annonce de nouvelle tâche
 */
class NewTaskMessage extends Message {
    private CoordinateurTaches.Task task;
    private static final String TYPE = "NEW_TASK";

    public NewTaskMessage(MyTransitRobot sender, CoordinateurTaches.Task task) {
        super(sender, TYPE);
        this.task = task;

        LogManager.getInstance().logCoordination(sender.getName(),
            "Annonce nouvelle tâche : " + task.getId());
    }

    public CoordinateurTaches.Task getTask() { return task; }

    @Override
    public String serialize() {
        return TYPE + "|" + task.getId() + "|" + task.getStartX() + "|" +
               task.getStartY() + "|" + task.getGoalX() + "|" + task.getGoalY();
    }

    @Override
    public void process(MyTransitRobot receiver) {
        receiver.getCoordinateurTaches().handleNewTask(task);
        LogManager.getInstance().logCoordination(receiver.getName(),
            "Nouvelle tâche reçue : " + task.getId());
    }
}

/**
 * Message d'assignation de tâche
 */
class TaskAssignedMessage extends Message {
    private String taskId;
    private String assignedRobotId;
    private static final String TYPE = "TASK_ASSIGNED";

    public TaskAssignedMessage(MyTransitRobot sender, String taskId, String assignedRobotId) {
        super(sender, TYPE);
        this.taskId = taskId;
        this.assignedRobotId = assignedRobotId;

        LogManager.getInstance().logCoordination(sender.getName(),
            "Assignation : tâche " + taskId + " à " + assignedRobotId);
    }

    public String getTaskId() { return taskId; }
    public String getAssignedRobotId() { return assignedRobotId; }

    @Override
    public String serialize() {
        return TYPE + "|" + taskId + "|" + assignedRobotId;
    }

    @Override
    public void process(MyTransitRobot receiver) {
        receiver.getCoordinateurTaches().handleTaskAssigned(taskId, assignedRobotId);
        LogManager.getInstance().logCoordination(receiver.getName(),
            "Assignation reçue : tâche " + taskId + " à " + assignedRobotId);
    }
}

/**
 * Message de tâche terminée
 */
class TaskCompletedMessage extends Message {
    private String taskId;
    private String robotId;
    private long deliveryTime;
    private double batteryUsed;
    private static final String TYPE = "TASK_COMPLETED";

    public TaskCompletedMessage(MyTransitRobot sender, String taskId, long deliveryTime, double batteryUsed) {
        super(sender, TYPE);
        this.taskId = taskId;
        this.robotId = sender.getName();
        this.deliveryTime = deliveryTime;
        this.batteryUsed = batteryUsed;

        LogManager.getInstance().logCoordination(sender.getName(),
            "Tâche terminée : " + taskId + " en " + (deliveryTime/1000) + "s");
    }

    public String getTaskId() { return taskId; }
    public String getRobotId() { return robotId; }
    public long getDeliveryTime() { return deliveryTime; }
    public double getBatteryUsed() { return batteryUsed; }

    @Override
    public String serialize() {
        return TYPE + "|" + taskId + "|" + robotId + "|" + deliveryTime + "|" + batteryUsed;
    }

    @Override
    public void process(MyTransitRobot receiver) {
        receiver.getCoordinateurTaches().handleTaskCompleted(robotId, taskId, deliveryTime, batteryUsed);
        LogManager.getInstance().logCoordination(receiver.getName(),
            "Tâche terminée reçue : " + taskId + " par " + robotId);
    }
}

/**
 * Message de mise à jour d'efficacité
 */
class EfficiencyUpdateMessage extends Message {
    private String robotId;
    private double efficiency;
    private int deliveryCount;
    private static final String TYPE = "EFFICIENCY_UPDATE";

    public EfficiencyUpdateMessage(MyTransitRobot sender, String robotId, double efficiency, int deliveryCount) {
        super(sender, TYPE);
        this.robotId = robotId;
        this.efficiency = efficiency;
        this.deliveryCount = deliveryCount;

        LogManager.getInstance().logCoordination(sender.getName(),
            "Partage efficacité : " + robotId + " = " + String.format("%.2f", efficiency) +
            " (" + deliveryCount + " livraisons)");
    }

    public String getRobotId() { return robotId; }
    public double getEfficiency() { return efficiency; }
    public int getDeliveryCount() { return deliveryCount; }

    @Override
    public String serialize() {
        return TYPE + "|" + robotId + "|" + efficiency + "|" + deliveryCount;
    }

    @Override
    public void process(MyTransitRobot receiver) {
        receiver.getCoordinateurTaches().handleEfficiencyUpdate(robotId, efficiency, deliveryCount);
        LogManager.getInstance().logCoordination(receiver.getName(),
            "Efficacité reçue : " + robotId + " = " + String.format("%.2f", efficiency) +
            " (" + deliveryCount + " livraisons)");
    }
}

/**
 * Message d'état des zones de transit
 */
class TransitZoneStatusMessage extends Message {
    private int x;
    private int y;
    private boolean isFull;
    private static final String TYPE = "TRANSIT_ZONE_STATUS";

    public TransitZoneStatusMessage(MyTransitRobot sender, int x, int y, boolean isFull) {
        super(sender, TYPE);
        this.x = x;
        this.y = y;
        this.isFull = isFull;

        LogManager.getInstance().logCoordination(sender.getName(),
            "État zone transit (" + x + "," + y + ") : " + (isFull ? "pleine" : "disponible"));
    }

    public int getX() { return x; }
    public int getY() { return y; }
    public boolean isFull() { return isFull; }

    @Override
    public String serialize() {
        return TYPE + "|" + x + "|" + y + "|" + isFull;
    }

    @Override
    public void process(MyTransitRobot receiver) {
        receiver.getCoordinateurTaches().handleTransitZoneStatus(x, y, isFull);
        LogManager.getInstance().logCoordination(receiver.getName(),
            "État zone transit reçu (" + x + "," + y + ") : " + (isFull ? "pleine" : "disponible"));
    }
}
