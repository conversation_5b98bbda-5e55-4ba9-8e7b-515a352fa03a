package Simulator;

import fr.emse.fayol.maqit.simulator.components.ColorPackage;
import fr.emse.fayol.maqit.simulator.components.PackageState;
import fr.emse.fayol.maqit.simulator.components.ColorTransitZone;
import fr.emse.fayol.maqit.simulator.components.ColorStartZone;
import fr.emse.fayol.maqit.simulator.components.Robot;
import fr.emse.fayol.maqit.simulator.environment.Cell;
import fr.emse.fayol.maqit.simulator.environment.ColorCell;
import fr.emse.fayol.maqit.simulator.environment.ColorGridEnvironment;

import java.awt.Color;
import java.util.List;

/**
 * Auteurs: Oussama GUELFAA, Houssam ALLALI & Noa AKAYAD
 * Date: 25-05-2025
 *
 * Notre robot principal qui gère tout : livraisons, zones de transit, batterie, communication.
 * C'est le cœur de notre projet ! Il prend ses décisions tout seul mais communique avec les autres.
 * On a essayé de faire quelque chose de pas trop compliqué mais qui marche bien.
 */
public class MyTransitRobot extends MyRobot {

    // Les différents états de notre robot (on a essayé de faire simple)
    public enum TransitState {
        FREE,               // Le robot ne fait rien, il cherche du boulot
        GOING_TO_START,    // Il va chercher un colis dans une zone de départ
        GOING_TO_TRANSIT,  // Il va déposer un colis dans une zone de transit
        GOING_TO_GOAL,     // Il va livrer directement à la destination finale
        WAITING_AT_TRANSIT,// Il attend à une zone de transit (pas utilisé finalement)
        PICKING_FROM_TRANSIT, // Il récupère un colis qu'un autre robot a déposé
        DELIVERED,         // Il vient de livrer, mission accomplie !
        RETURNING_TO_CENTER // Il retourne au centre pour chercher une nouvelle tâche
    }

    // Variables pour savoir ce que fait le robot
    private TransitState transitState;
    private int transitX;  // Position X de la zone de transit qu'on vise
    private int transitY;  // Position Y de la zone de transit qu'on vise
    private double batteryLevel = 100.0;  // Niveau de batterie (100% au début)

    // Messages qu'on pourrait envoyer (on les a pas tous utilisés finalement)
    private static final String MSG_HELP_REQUEST = "HELP_REQUEST";
    private static final String MSG_HELP_OFFER = "HELP_OFFER";
    private static final String MSG_TRANSIT_FULL = "TRANSIT_FULL";
    private static final String MSG_TRANSIT_AVAILABLE = "TRANSIT_AVAILABLE";
    private static final String MSG_LOW_BATTERY = "LOW_BATTERY";
    private boolean waitingForHelp = false;  // Pas utilisé non plus

    // Chaque robot a son propre coordinateur pour gérer les tâches
    private CoordinateurTaches taskCoordinator;

    // Positions fixes des zones de transit (on les a mises au centre de la carte)
    int[][] transitZones = {{12, 10}, {12, 9}, {9, 10}, {9, 9}};
    // Toutes les stations de recharge qu'on a placées dans l'entrepôt
    int[][] chargingStations = {
        {11, 10}, {13, 9}, {8, 10}, {10, 9},     // Stations près du centre
        {2, 2}, {17, 2}, {2, 17}, {17, 17},      // Stations dans les coins
        {10, 2}, {2, 10}, {17, 10}, {10, 17},    // Stations sur les bords
        {5, 5}, {15, 5}, {5, 15}, {15, 15}       // Stations intermédiaires
    };

    // Zone où les robots retournent quand ils ont fini leur boulot
    private static final int CENTRAL_AREA_X = 10;
    private static final int CENTRAL_AREA_Y = 12;
    private boolean isCharging = false;  // Pour savoir si le robot est en train de charger

    // Paramètres pour la gestion de la batterie (on a testé plusieurs valeurs)
    private static final double MAX_BATTERY = 100.0;           // Batterie pleine
    private static final double CRITICAL_BATTERY_THRESHOLD = 5.0;  // Urgence ! Il faut charger
    private static final double LOW_BATTERY_THRESHOLD = 15.0;      // Bientôt vide
    private static final double MOVE_BATTERY_COST = 0.4;           // Coût pour bouger d'une case
    private static final double PICKUP_BATTERY_COST = 1.0;        // Coût pour prendre un colis
    private static final double DEPOSIT_BATTERY_COST = 1.0;       // Coût pour déposer un colis
    private static final double CHARGING_RATE = 10.0;             // Vitesse de recharge

    public MyTransitRobot(String name, int field, int debug, int[] pos, Color color, int rows, int columns, ColorGridEnvironment env, long seed) {
        super(name, field, debug, pos, color, rows, columns, env, seed);
        this.transitState = TransitState.FREE;
        this.batteryLevel = MAX_BATTERY;
        this.taskCoordinator = new CoordinateurTaches(this, env);
        LogManager.getInstance().logAction(name, "Robot initialisé avec coordinateur");
    }

    public CoordinateurTaches getTaskCoordinator() {
        return taskCoordinator;
    }

    public CoordinateurTaches getCoordinateurTaches() {
        return taskCoordinator;
    }

    // Envoyer un message à tous les autres robots
    public void broadcastMessage(Message message) {
        List<Robot> robots = environnement.getRobot();
        int robotsContactes = 0;

        for (Robot robot : robots) {
            if (robot != this && robot instanceof MyTransitRobot) {
                ((MyTransitRobot)robot).handleMessage(message);
                robotsContactes++;
            }
        }

        LogManager.getInstance().logCoordination(this.getName(),
            "Message diffusé à " + robotsContactes + " robots de type " + message.getMessageType());
    }

    // Envoyer un message direct à un robot spécifique
    public void sendDirectMessage(Message message, String targetRobotId) {
        List<Robot> robots = environnement.getRobot();

        for (Robot robot : robots) {
            if (robot instanceof MyTransitRobot && robot.getName().equals(targetRobotId)) {
                ((MyTransitRobot)robot).handleMessage(message);
                LogManager.getInstance().logCoordination(this.getName(),
                    "Message direct envoyé à " + targetRobotId + " de type " + message.getMessageType());
                break;
            }
        }
    }

    public double getBatteryLevel() {
        return batteryLevel;
    }

    public boolean hasDelivered() {
        return etatActuel == EtatRobot.LIVRE;
    }

    // Trouver une zone de transit qui n'est pas pleine
    private ColorTransitZone findTransitZoneNotFull() {
        for (int[] pos : transitZones) {
            Cell cell = environnement.getGrid()[pos[0]][pos[1]];

            if (cell instanceof ColorCell && ((ColorCell)cell).getContent() instanceof ColorTransitZone) {
                ColorTransitZone zone = (ColorTransitZone) ((ColorCell)cell).getContent();

                if (!zone.isFull()) {
                    transitX = pos[0];
                    transitY = pos[1];
                    return zone;
                }
            }
        }
        return null;
    }

    // Trouver une zone de transit avec des colis
    private ColorTransitZone findTransitZoneWithPackage() {
        for (int[] pos : transitZones) {
            Cell cell = environnement.getGrid()[pos[0]][pos[1]];

            if (cell instanceof ColorCell && ((ColorCell)cell).getContent() instanceof ColorTransitZone) {
                ColorTransitZone zone = (ColorTransitZone) ((ColorCell)cell).getContent();

                if (zone.getPackages().size() > 0) {
                    transitX = pos[0];
                    transitY = pos[1];
                    return zone;
                }
            }
        }
        return null;
    }

    // Vérifier si toutes les zones de transit sont pleines
    private boolean transitZonesAreFull() {
        for (int[] pos : transitZones) {
            Cell cell = environnement.getGrid()[pos[0]][pos[1]];

            if (cell instanceof ColorCell && cell.getContent() instanceof ColorTransitZone) {
                ColorTransitZone zone = (ColorTransitZone) cell.getContent();

                if (!zone.isFull()) {
                    LogManager.getInstance().logTransit(getName(),
                        "Zone (" + pos[0] + "," + pos[1] + ")",
                        "zone de transit disponible trouvée");
                    return false;
                }
            }
        }

        LogManager.getInstance().logTransit(getName(), "Toutes zones",
            "toutes les zones de transit sont pleines");
        return true;
    }

    // Décider s'il vaut mieux utiliser une zone de transit
    private boolean isBetterToUseTransit(int destX, int destY) {
        double distanceDirecte = distanceTo(this.getX(), this.getY(), destX, destY);

        LogManager.getInstance().logTransit(getName(), "Calcul trajet",
            String.format("Distance directe vers destination (%d,%d) : %.1f", destX, destY, distanceDirecte));

        ColorTransitZone zoneDisponible = findTransitZoneNotFull();
        if (zoneDisponible == null) {
            LogManager.getInstance().logTransit(getName(), "Aucune zone",
                "aucune zone de transit disponible, livraison directe");
            return false;
        }

        double distanceVersTransit = distanceTo(this.getX(), this.getY(), transitX, transitY);
        double distanceTransitVersDestination = distanceTo(transitX, transitY, destX, destY);
        double distanceTotaleViaTransit = distanceVersTransit + distanceTransitVersDestination;

        LogManager.getInstance().logTransit(getName(),
            "Zone (" + transitX + "," + transitY + ")",
            String.format("Distance via transit : %.1f (%.1f + %.1f)",
                distanceTotaleViaTransit, distanceVersTransit, distanceTransitVersDestination));

        double seuil = distanceDirecte * 1.3;
        boolean utiliserTransit = distanceTotaleViaTransit <= seuil;

        if (utiliserTransit) {
            LogManager.getInstance().logTransit(getName(), "Décision",
                "utilisation de la zone de transit recommandée");
        } else {
            LogManager.getInstance().logTransit(getName(), "Décision",
                "livraison directe recommandée");
        }

        return utiliserTransit;
    }

    public ColorPackage getCarriedPackage() {
        return colisTransporte;
    }

    public void setCarriedPackage(ColorPackage pack) {
        this.colisTransporte = pack;
    }

    @Override
    public void step() {
        // D'abord, on vérifie si le robot est coincé quelque part
        if (isStuck()) {
            tryToUnstuck();  // On essaie de se débloquer
            return;
        }

        // Si le robot retourne au centre après avoir livré un colis
        if (transitState == TransitState.RETURNING_TO_CENTER) {
            if (isNearCentralArea()) {
                // On est arrivé au centre, on peut chercher une nouvelle tâche
                transitState = TransitState.FREE;
                etatActuel = EtatRobot.LIBRE;
                LogManager.getInstance().logAction(getName(), "arrivé à la zone centrale, prêt pour nouvelle tâche");
            } else {
                // On continue vers le centre
                moveTowardsCentralArea();
            }
            return;
        }

        // Gestion de la batterie
        if (isCharging) {
            if (batteryLevel < MAX_BATTERY * 0.5) {
                chargeBattery();
                return;
            } else {
                isCharging = false;
            }
        }

        // Charger si batterie critique
        if (batteryLevel <= CRITICAL_BATTERY_THRESHOLD) {
            if (isNearChargingStation()) {
                isCharging = true;
                LogManager.getInstance().logBattery(getName(), batteryLevel, "commence à charger");
                chargeBattery();
                return;
            } else {
                int[] nearestCS = findNearestChargingStation();
                if (nearestCS != null) {
                    moveOneStepTo(nearestCS[0], nearestCS[1]);
                    consumeBatteryForMovement();
                    return;
                }
            }
        }

        // Robot libre - chercher un colis
        if (etatActuel == EtatRobot.LIBRE) {
            // D'abord vérifier les zones de transit
            ColorTransitZone transitWithPackage = findTransitZoneWithPackage();

            if (transitWithPackage != null) {
                if (isAdjacentTo(transitX, transitY)) {
                    // Prendre le colis de la zone de transit
                    List<ColorPackage> packages = transitWithPackage.getPackages();
                    if (!packages.isEmpty()) {
                        colisTransporte = packages.get(0);
                        transitWithPackage.removePackage(colisTransporte);
                        consumeBatteryForPickup();
                        momentDepart = System.currentTimeMillis();

                        int[] goalPos = DESTINATIONS.get(colisTransporte.getDestinationGoalId());
                        if (goalPos != null) {
                            destinationX = goalPos[0];
                            destinationY = goalPos[1];
                            etatActuel = EtatRobot.TRANSPORT;
                            transitState = TransitState.GOING_TO_GOAL;
                        }

                        LogManager.getInstance().logDelivery(getName(), colisTransporte.getId() + "",
                            "pris colis de la zone de transit (" + transitX + "," + transitY + ")");

                        TransitZoneStatusMessage message = new TransitZoneStatusMessage(this, transitX, transitY, false);
                        broadcastMessage(message);
                    }
                } else {
                    moveOneStepTo(transitX, transitY);
                }
            } else {
                // Vérifier les zones de départ
                ColorStartZone startZone = findStartZoneWithPackage();
                if (startZone == null) {
                    taskCoordinator.findBestTaskForRobot();
                    return;
                }

                if (isAdjacentTo(startZone.getX(), startZone.getY())) {
                    // Prendre le colis de la zone de départ
                    if (!startZone.getPackages().isEmpty()) {
                        colisTransporte = startZone.getPackages().get(0);
                        startZone.removePackage(colisTransporte);
                        consumeBatteryForPickup();
                        momentDepart = System.currentTimeMillis();

                        int[] goalPos = DESTINATIONS.get(colisTransporte.getDestinationGoalId());
                        if (goalPos != null) {
                            destinationX = goalPos[0];
                            destinationY = goalPos[1];

                            // Créer une tâche
                            CoordinateurTaches.Task task = taskCoordinator.createTask(
                                colisTransporte, startZone, DESTINATIONS);

                            // Décider d'utiliser une zone de transit
                            ColorTransitZone tz = findTransitZoneNotFull();

                            if (tz != null && taskCoordinator.shouldUseTransitZone(this, destinationX, destinationY, transitX, transitY)) {
                                etatActuel = EtatRobot.TRANSPORT;
                                transitState = TransitState.GOING_TO_TRANSIT;
                                LogManager.getInstance().logCoordination(getName(), "Pris colis, va vers zone de transit");
                            } else {
                                etatActuel = EtatRobot.TRANSPORT;
                                transitState = TransitState.GOING_TO_GOAL;
                                LogManager.getInstance().logCoordination(getName(), "Pris colis, livraison directe");
                            }
                        }
                    }
                } else {
                    moveOneStepTo(startZone.getX(), startZone.getY());
                }
            }
        } else if (etatActuel == EtatRobot.TRANSPORT) {
            // Robot transporte un colis
            if (transitState == TransitState.GOING_TO_TRANSIT) {
                // Va vers zone de transit
                if (isAdjacentTo(transitX, transitY)) {
                    Cell c = environnement.getGrid()[transitX][transitY];
                    if (c instanceof ColorCell && c.getContent() instanceof ColorTransitZone) {
                        ColorTransitZone tz = (ColorTransitZone) c.getContent();
                        if (!tz.isFull()) {
                            tz.addPackage(colisTransporte);
                            consumeBatteryForDeposit();
                            LogManager.getInstance().logDelivery(getName(), colisTransporte.getId() + "",
                                "déposé colis dans zone de transit (" + transitX + "," + transitY + ")");

                            boolean isFull = tz.isFull();
                            TransitZoneStatusMessage message = new TransitZoneStatusMessage(this, transitX, transitY, isFull);
                            broadcastMessage(message);

                            colisTransporte = null;
                            etatActuel = EtatRobot.LIBRE;
                            transitState = TransitState.FREE;
                        }
                    }
                } else {
                    moveOneStepTo(transitX, transitY);
                }
            } else if (transitState == TransitState.GOING_TO_GOAL) {
                // Va vers destination finale
                if ((this.getX() == destinationX) && (this.getY() == destinationY)) {
                    // Livraison réussie
                    colisTransporte.setState(PackageState.ARRIVED);
                    consumeBatteryForDeposit();
                    MySimFactory.deliveredCount++;

                    momentArrivee = System.currentTimeMillis();
                    long deliveryTime = momentArrivee - momentDepart;
                    double batteryUsed = MAX_BATTERY - batteryLevel;

                    LogManager.getInstance().logDelivery(getName(), colisTransporte.getId() + "",
                        "livré en " + (deliveryTime/1000) + "s avec " +
                        String.format("%.1f", batteryUsed) + "% batterie");

                    taskCoordinator.updateRobotEfficiency(this, deliveryTime, batteryUsed);

                    TaskCompletedMessage message = new TaskCompletedMessage(
                        this, "Task-" + colisTransporte.getId(), deliveryTime, batteryUsed);
                    broadcastMessage(message);

                    colisTransporte = null;
                    isCharging = false;
                    transitState = TransitState.RETURNING_TO_CENTER;
                    LogManager.getInstance().logAction(getName(), "livré, retourne au centre");
                } else {
                    // Vérifier batterie pour atteindre destination
                    if (batteryLevel < CRITICAL_BATTERY_THRESHOLD || !canReachDestination(destinationX, destinationY)) {
                        if (isNearChargingStation()) {
                            isCharging = true;
                            LogManager.getInstance().logBattery(getName(), batteryLevel, "recharge avant destination");
                            return;
                        } else {
                            int[] nearestCS = findNearestChargingStation();
                            if (nearestCS != null) {
                                LogManager.getInstance().logBattery(getName(), batteryLevel, "va vers station de recharge");
                                moveOneStepTo(nearestCS[0], nearestCS[1]);
                                return;
                            }
                        }
                    }
                    moveOneStepTo(destinationX, destinationY);
                    consumeBatteryForMovement();
                }
            }
        }
    }

    // Méthodes utilitaires simples

    private boolean isNearCentralArea() {
        int distance = Math.abs(this.getX() - CENTRAL_AREA_X) + Math.abs(this.getY() - CENTRAL_AREA_Y);
        return distance <= 1;
    }

    private void moveTowardsCentralArea() {
        moveOneStepTo(CENTRAL_AREA_X, CENTRAL_AREA_Y);
        consumeBatteryForMovement();
        LogManager.getInstance().logTransit(getName(), "Zone centrale", "se dirige vers la zone centrale");
    }

    // Variables pour détecter si le robot est bloqué
    private int stuckCounter = 0;
    private int lastX = -1;
    private int lastY = -1;

    private boolean isStuck() {
        if (lastX == this.getX() && lastY == this.getY()) {
            stuckCounter++;
            return stuckCounter > 5;
        } else {
            stuckCounter = 0;
            lastX = this.getX();
            lastY = this.getY();
            return false;
        }
    }

    private void tryToUnstuck() {
        randomOrientation();
        if (freeForward()) {
            moveForward();
            consumeBatteryForMovement();
            LogManager.getInstance().logAction(getName(), "essaie de se débloquer");
        }
    }

    private void chargeBattery() {
        double oldLevel = batteryLevel;
        batteryLevel = Math.min(MAX_BATTERY, batteryLevel + CHARGING_RATE);
        LogManager.getInstance().logCharging(getName(), oldLevel, batteryLevel, "en cours de charge");
    }

    private boolean isNearChargingStation() {
        for (int[] station : chargingStations) {
            if (Math.abs(this.getX() - station[0]) <= 1 && Math.abs(this.getY() - station[1]) <= 1) {
                return true;
            }
        }
        return false;
    }

    private int[] findNearestChargingStation() {
        int[] nearest = null;
        double minDistance = Double.MAX_VALUE;

        for (int[] station : chargingStations) {
            double distance = distanceTo(this.getX(), this.getY(), station[0], station[1]);
            if (distance < minDistance) {
                minDistance = distance;
                nearest = station;
            }
        }
        return nearest;
    }

    public void consumeBatteryForMovement() {
        batteryLevel = Math.max(0, batteryLevel - MOVE_BATTERY_COST);
        LogManager.getInstance().logBattery(getName(), batteryLevel, "mouvement");
    }

    private void consumeBatteryForPickup() {
        batteryLevel = Math.max(0, batteryLevel - PICKUP_BATTERY_COST);
        LogManager.getInstance().logBattery(getName(), batteryLevel, "prise de colis");
    }

    private void consumeBatteryForDeposit() {
        batteryLevel = Math.max(0, batteryLevel - DEPOSIT_BATTERY_COST);
        LogManager.getInstance().logBattery(getName(), batteryLevel, "dépôt de colis");
    }

    private boolean canReachDestination(int destX, int destY) {
        double distance = distanceTo(this.getX(), this.getY(), destX, destY);
        double batteryNeeded = distance * MOVE_BATTERY_COST + DEPOSIT_BATTERY_COST;
        return batteryLevel >= batteryNeeded;
    }

    // Gérer les messages reçus d'autres robots
    public void handleMessage(Message message) {
        if (message instanceof NewTaskMessage) {
            NewTaskMessage taskMsg = (NewTaskMessage) message;
            taskCoordinator.handleNewTask(taskMsg.getTask());
        } else if (message instanceof MessageOffre) {
            MessageOffre bidMsg = (MessageOffre) message;
            taskCoordinator.handleBid(bidMsg.getBid());
        } else if (message instanceof TaskAssignedMessage) {
            TaskAssignedMessage assignMsg = (TaskAssignedMessage) message;
            taskCoordinator.handleTaskAssigned(assignMsg.getTaskId(), assignMsg.getAssignedRobotId());
        } else if (message instanceof TaskCompletedMessage) {
            TaskCompletedMessage completeMsg = (TaskCompletedMessage) message;
            taskCoordinator.handleTaskCompleted(completeMsg.getRobotId(), completeMsg.getTaskId(),
                completeMsg.getDeliveryTime(), completeMsg.getBatteryUsed());
        } else if (message instanceof EfficiencyUpdateMessage) {
            EfficiencyUpdateMessage effMsg = (EfficiencyUpdateMessage) message;
            taskCoordinator.handleEfficiencyUpdate(effMsg.getRobotId(), effMsg.getEfficiency(), effMsg.getDeliveryCount());
        } else if (message instanceof TransitZoneStatusMessage) {
            TransitZoneStatusMessage transitMsg = (TransitZoneStatusMessage) message;
            taskCoordinator.handleTransitZoneStatus(transitMsg.getX(), transitMsg.getY(), transitMsg.isFull());
        }
    }
}